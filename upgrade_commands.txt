# 在宿主机上执行这些命令来升级到 Claude Code 支持版本

# 1. 查找当前容器ID
docker ps

# 2. 备份数据 (替换 <container_id> 为实际容器ID)
docker cp <container_id>:/home/<USER>/fixmorph ./fixmorph_backup

# 3. 进入备份目录
cd ./fixmorph_backup

# 4. 构建新容器
docker build -f Dockerfile.claude -t fixmorph:claude .

# 5. 运行新容器
docker run -it -v $(pwd):/home/<USER>/fixmorph fixmorph:claude bash

# 6. 在新容器中测试 Claude Code
node --version
claude --help
cursor --list-extensions
