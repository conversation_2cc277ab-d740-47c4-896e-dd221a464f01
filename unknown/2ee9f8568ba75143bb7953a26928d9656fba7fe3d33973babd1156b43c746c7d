#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Git仓库管理模块

负责克隆仓库、切换版本、管理多个项目的Git操作
"""

import os
import shutil
import subprocess
import logging
from pathlib import Path
from typing import Dict, Optional, List
import hashlib
import sys
import threading
import time

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))
from src_enhanced import config


class GitManager:
    """Git仓库管理器"""
    
    def __init__(self, 
                 cache_dir: str = "experiments/enhanced_dataset/shared_repos",
                 github_token: Optional[str] = None):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.logger = self._setup_logger()
        
        # GitHub认证配置
        self.github_token = github_token or config.get_github_token()
        if self.github_token:
            self.logger.info("🔐 GitHub token configured for authenticated access")
        else:
            self.logger.warning("⚠️ No GitHub token found - using anonymous access (limited to 60 requests/hour)")
            self.logger.info("💡 运行 'python src_enhanced/setup_token.py' 来配置GitHub token")
        
        # 仓库缓存映射 {repo_url: local_path}
        self.repo_cache = {}
        
        # 并发锁：防止多个worker同时克隆同一仓库
        self._clone_locks = {}
        self._lock = threading.RLock()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('GitManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _get_authenticated_url(self, repo_url: str) -> str:
        """将仓库URL转换为认证URL"""
        if not self.github_token:
            return repo_url
            
        # 处理GitHub仓库URL
        if 'github.com' in repo_url:
            if repo_url.startswith('https://github.com/'):
                # 格式：https://<EMAIL>/owner/repo.git
                repo_path = repo_url.replace('https://github.com/', '')
                return f"https://{self.github_token}@github.com/{repo_path}"
            elif repo_url.startswith('**************:'):
                # SSH URL转换为HTTPS认证URL
                repo_path = repo_url.replace('**************:', '').replace('.git', '')
                return f"https://{self.github_token}@github.com/{repo_path}.git"
        
        return repo_url
    
    def _execute_command(self, command: str, cwd: Optional[str] = None) -> tuple:
        """执行shell命令"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                cwd=cwd,
                capture_output=True, 
                text=True, 
                timeout=300  # 5分钟超时
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            self.logger.error(f"Command timeout: {command}")
            return -1, "", "Command timeout"
        except Exception as e:
            self.logger.error(f"Command execution failed: {e}")
            return -1, "", str(e)
    
    def _get_repo_cache_path(self, repo_url: str) -> Path:
        """获取仓库缓存路径"""
        # 使用URL的hash作为目录名，避免特殊字符问题
        repo_hash = hashlib.md5(repo_url.encode()).hexdigest()[:12]
        
        # 提取仓库名称作为可读标识
        repo_name = repo_url.split('/')[-1].replace('.git', '')
        if not repo_name:
            repo_name = "unknown"
            
        cache_name = f"{repo_name}_{repo_hash}"
        return self.cache_dir / cache_name
    
    def clone_or_update_repo(self, repo_url: str) -> Optional[Path]:
        """克隆或更新仓库到缓存目录（线程安全）"""
        cache_path = self._get_repo_cache_path(repo_url)
        
        # 获取或创建该仓库的锁
        with self._lock:
            if repo_url not in self._clone_locks:
                self._clone_locks[repo_url] = threading.RLock()
            repo_lock = self._clone_locks[repo_url]
        
        # 使用仓库特定的锁来防止并发克隆
        with repo_lock:
            try:
                # 双重检查：锁内再次检查目录状态
                if cache_path.exists():
                    # 检查是否是有效的Git仓库
                    ret_code, stdout, stderr = self._execute_command("git status", str(cache_path))
                    if ret_code == 0:
                        self.logger.info(f"Using existing valid repo cache: {cache_path}")
                        self.repo_cache[repo_url] = cache_path
                        return cache_path
                    else:
                        # 目录存在但不是有效仓库，删除重建
                        self.logger.warning(f"Invalid repo cache found, removing: {cache_path}")
                        shutil.rmtree(cache_path)
                
                # 目录不存在或已清理，进行克隆
                return self._clone_fresh_repo(repo_url, cache_path)
                
            except Exception as e:
                self.logger.error(f"Failed to clone/update repo {repo_url}: {e}")
                return None
    
    def _clone_fresh_repo(self, repo_url: str, cache_path: Path) -> Optional[Path]:
        """克隆新的仓库"""
        self.logger.info(f"Cloning repo {repo_url} to {cache_path}")
        
        # 确保父目录存在
        cache_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 如果目录已存在，先删除（防止残留）
        if cache_path.exists():
            self.logger.warning(f"Removing existing directory: {cache_path}")
            shutil.rmtree(cache_path)
        
        # 🔐 使用认证URL（如果配置了GitHub token）
        auth_url = self._get_authenticated_url(repo_url)
        
        # 对于CVE实验，需要完整的Git历史以访问特定commit
        clone_command = f"git clone {auth_url} {cache_path}"
        
        # 注意：移除了 --depth=1 参数，确保可以访问历史commit
        
        ret_code, stdout, stderr = self._execute_command(clone_command)
        
        if ret_code != 0:
            self.logger.error(f"Failed to clone {repo_url}: {stderr}")
            # 如果认证克隆失败，尝试原始URL（可能是私有仓库问题）
            if auth_url != repo_url:
                self.logger.info("Retrying with original URL...")
                retry_command = f"git clone {repo_url} {cache_path}"
                ret_code, stdout, stderr = self._execute_command(retry_command)
                if ret_code != 0:
                    self.logger.error(f"Retry also failed: {stderr}")
                    return None
            else:
                return None
        
        # 验证克隆成功
        if not cache_path.exists() or not (cache_path / ".git").exists():
            self.logger.error(f"Clone verification failed: {cache_path}")
            return None
            
        # 克隆成功后，获取完整历史（如果需要）
        self._unshallow_if_needed(cache_path)
        
        # 更新缓存映射
        self.repo_cache[repo_url] = cache_path
        self.logger.info(f"✅ Successfully cloned {repo_url}")
        
        return cache_path
    
    def _unshallow_if_needed(self, repo_path: Path):
        """如果需要，将浅克隆转换为完整克隆"""
        try:
            # 检查是否是浅克隆
            ret_code, stdout, stderr = self._execute_command(
                "git rev-parse --is-shallow-repository", str(repo_path)
            )
            
            if ret_code == 0 and stdout.strip() == "true":
                self.logger.info(f"Converting shallow clone to full clone: {repo_path}")
                ret_code, stdout, stderr = self._execute_command(
                    "git fetch --unshallow", str(repo_path)
                )
                if ret_code != 0:
                    self.logger.warning(f"Failed to unshallow repo: {stderr}")
                    
        except Exception as e:
            self.logger.warning(f"Failed to check/unshallow repo: {e}")
    
    def checkout_commit(self, repo_path: Path, commit_hash: str) -> bool:
        """切换到指定commit"""
        try:
            self.logger.info(f"Checking out commit {commit_hash} in {repo_path}")
            
            # 首先尝试直接checkout
            ret_code, stdout, stderr = self._execute_command(
                f"git checkout {commit_hash}", str(repo_path)
            )
            
            if ret_code == 0:
                return True
                
            # 如果失败，尝试fetch特定commit
            self.logger.info(f"Direct checkout failed, trying to fetch commit {commit_hash}")
            ret_code, stdout, stderr = self._execute_command(
                f"git fetch origin {commit_hash}", str(repo_path)
            )
            
            if ret_code == 0:
                # 再次尝试checkout
                ret_code, stdout, stderr = self._execute_command(
                    f"git checkout {commit_hash}", str(repo_path)
                )
                return ret_code == 0
                
            self.logger.error(f"Failed to checkout commit {commit_hash}: {stderr}")
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking out commit {commit_hash}: {e}")
            return False
    
    def create_working_copy(self, repo_url: str, commit_hash: str, target_dir: Path) -> bool:
        """创建特定commit的工作副本"""
        try:
            # 确保仓库缓存存在
            cache_path = self.clone_or_update_repo(repo_url)
            if not cache_path:
                return False
            
            # 创建目标目录
            if target_dir.exists():
                shutil.rmtree(target_dir)
            target_dir.parent.mkdir(parents=True, exist_ok=True)
            
            # 复制仓库到工作目录
            self.logger.info(f"Creating working copy at {target_dir}")
            shutil.copytree(cache_path, target_dir)
            
            # 切换到指定commit
            if not self.checkout_commit(target_dir, commit_hash):
                self.logger.error(f"Failed to checkout {commit_hash} in working copy")
                return False
                
            self.logger.info(f"✅ Created working copy for {commit_hash} at {target_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create working copy: {e}")
            return False
    
    def setup_cve_experiment(self, cve_data: Dict, experiment_dir: Path) -> bool:
        """为CVE实验设置所有必要的工作目录"""
        try:
            cve_id = cve_data.get('cve_id', 'unknown')
            self.logger.info(f"Setting up experiment for {cve_id}")
            
            # 获取仓库URL（假设source和target使用相同仓库）
            repo_url = cve_data.get('source_repo')
            if not repo_url:
                self.logger.error(f"No repository URL found for {cve_id}")
                return False
            
            # 创建实验目录
            experiment_dir.mkdir(parents=True, exist_ok=True)
            
            # 设置各个版本的工作目录
            versions = [
                ('pa', cve_data.get('pa')),  # 源版本漏洞commit
                ('pb', cve_data.get('pb')),  # 源版本修复commit
                ('pc', cve_data.get('pc')),  # 目标版本漏洞commit
                ('pe', cve_data.get('pe')),  # 目标版本修复commit
            ]
            
            success_count = 0
            for version_name, commit_hash in versions:
                if not commit_hash:
                    self.logger.warning(f"No commit hash for {version_name} in {cve_id}")
                    continue
                    
                version_dir = experiment_dir / version_name
                if self.create_working_copy(repo_url, commit_hash, version_dir):
                    success_count += 1
                else:
                    self.logger.error(f"Failed to create {version_name} for {cve_id}")
            
            # 至少需要pa, pb, pc三个版本才能进行实验
            if success_count >= 3:
                self.logger.info(f"✅ Successfully set up {success_count} versions for {cve_id}")
                return True
            else:
                self.logger.error(f"❌ Only {success_count} versions set up for {cve_id}, need at least 3")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to setup CVE experiment: {e}")
            return False
    
    def cleanup_experiment(self, experiment_dir: Path):
        """清理实验目录"""
        try:
            if experiment_dir.exists():
                self.logger.info(f"Cleaning up experiment directory: {experiment_dir}")
                shutil.rmtree(experiment_dir)
        except Exception as e:
            self.logger.warning(f"Failed to cleanup {experiment_dir}: {e}")
    
    def get_cache_info(self) -> Dict:
        """获取缓存信息"""
        cache_info = {
            'cache_dir': str(self.cache_dir),
            'cached_repos': []
        }
        
        try:
            for repo_dir in self.cache_dir.iterdir():
                if repo_dir.is_dir():
                    # 获取仓库信息
                    ret_code, stdout, stderr = self._execute_command(
                        "git remote get-url origin", str(repo_dir)
                    )
                    repo_url = stdout.strip() if ret_code == 0 else "unknown"
                    
                    cache_info['cached_repos'].append({
                        'name': repo_dir.name,
                        'path': str(repo_dir),
                        'url': repo_url
                    })
        except Exception as e:
            self.logger.warning(f"Failed to get cache info: {e}")
            
        return cache_info
