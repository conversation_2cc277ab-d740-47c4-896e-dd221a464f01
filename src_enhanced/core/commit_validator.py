#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CVE Commit验证和补全器

根据用户建议实现：
1. 验证commit在目标仓库中的有效性
2. 使用GitHub API自动补全缺失或无效的commit
3. 基于已知commit推导相关的修复commit
"""

import subprocess
import logging
import requests
import time
from typing import Dict, List, Optional, Tuple
from pathlib import Path


class CommitValidator:
    """Commit验证和补全器"""
    
    def __init__(self, repo_path: str, github_token: str = None):
        """
        初始化验证器
        
        Args:
            repo_path: 本地Git仓库路径 
            github_token: GitHub API token (可选，用于API调用)
        """
        self.repo_path = Path(repo_path)
        self.github_token = github_token
        self.logger = self._setup_logger()
        
        # GitHub API相关
        self.github_api_base = "https://api.github.com"
        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'FixMorph-CommitValidator/1.0'
        }
        if github_token:
            self.headers['Authorization'] = f'token {github_token}'
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('CommitValidator')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def validate_commit(self, commit_hash: str) -> bool:
        """
        验证commit在本地仓库中是否存在
        
        Args:
            commit_hash: 要验证的commit哈希
            
        Returns:
            bool: commit是否有效
        """
        try:
            result = subprocess.run(
                ['git', 'cat-file', '-e', commit_hash],
                cwd=self.repo_path,
                capture_output=True,
                timeout=10
            )
            return result.returncode == 0
        except Exception as e:
            self.logger.error(f"Error validating commit {commit_hash}: {e}")
            return False
    
    def find_related_commits_by_file(self, repo_owner: str, repo_name: str, 
                                   known_commit: str, file_path: str, 
                                   search_window_days: int = 30) -> List[Dict]:
        """
        通过文件历史查找相关的commits
        
        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称 
            known_commit: 已知的commit哈希
            file_path: 相关文件路径
            search_window_days: 搜索时间窗口(天)
            
        Returns:
            List[Dict]: 相关commits列表
        """
        try:
            # 获取known_commit的时间
            commit_info_url = f"{self.github_api_base}/repos/{repo_owner}/{repo_name}/commits/{known_commit}"
            response = requests.get(commit_info_url, headers=self.headers)
            
            if response.status_code != 200:
                self.logger.error(f"Failed to get commit info: {response.status_code}")
                return []
            
            commit_data = response.json()
            commit_date = commit_data['commit']['author']['date']
            
            # 计算搜索时间范围
            from datetime import datetime, timedelta
            base_date = datetime.strptime(commit_date, '%Y-%m-%dT%H:%M:%SZ')
            since_date = (base_date - timedelta(days=search_window_days)).isoformat() + 'Z'
            until_date = (base_date + timedelta(days=search_window_days)).isoformat() + 'Z'
            
            # 搜索文件相关的commits
            commits_url = f"{self.github_api_base}/repos/{repo_owner}/{repo_name}/commits"
            params = {
                'path': file_path,
                'since': since_date,
                'until': until_date,
                'per_page': 100
            }
            
            response = requests.get(commits_url, headers=self.headers, params=params)
            if response.status_code != 200:
                self.logger.error(f"Failed to search commits: {response.status_code}")
                return []
            
            commits = response.json()
            self.logger.info(f"Found {len(commits)} related commits for file {file_path}")
            
            return commits
            
        except Exception as e:
            self.logger.error(f"Error searching related commits: {e}")
            return []
    
    def find_fix_commit_by_message(self, repo_owner: str, repo_name: str, 
                                 cve_id: str, known_commit: str) -> Optional[str]:
        """
        通过commit message关键词查找修复commit
        
        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称
            cve_id: CVE编号
            known_commit: 已知相关commit
            
        Returns:
            Optional[str]: 找到的修复commit哈希
        """
        try:
            # 构建搜索关键词
            search_terms = [
                f"{cve_id}",
                f"fix {cve_id}",
                f"CVE-{cve_id[4:]}",  # 去掉CVE-前缀
            ]
            
            for term in search_terms:
                # 使用GitHub搜索API
                search_url = f"{self.github_api_base}/search/commits"
                params = {
                    'q': f'repo:{repo_owner}/{repo_name} {term}',
                    'sort': 'committer-date',
                    'per_page': 10
                }
                
                response = requests.get(search_url, headers=self.headers, params=params)
                if response.status_code == 200:
                    results = response.json()
                    if results['items']:
                        for item in results['items']:
                            commit_hash = item['sha']
                            if self.validate_commit(commit_hash):
                                self.logger.info(f"Found valid fix commit for {cve_id}: {commit_hash}")
                                return commit_hash
                
                # API限制，稍微等待
                time.sleep(1)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error searching fix commit: {e}")
            return None
    
    def supplement_commit_data(self, cve_data: Dict) -> Dict:
        """
        根据用户建议补全CVE数据中的缺失commit
        
        在source/target内部进行双向补全：
        - source.vulnerable + source.patched 可以相互补全
        - target.vulnerable + target.patched 可以相互补全
        - 如果某个source/target中两个commit都缺失，跳过该CVE
        
        Args:
            cve_data: CVE数据字典
            
        Returns:
            Dict: 补全后的CVE数据，如果无法补全则返回None
        """
        cve_id = cve_data.get('cve_id', 'unknown')
        self.logger.info(f"🔍 开始补全 {cve_id} 的commit数据")
        
        # 提取仓库信息
        repo_url = cve_data.get('projects', {}).get('source', {}).get('repo', '')
        if 'github.com' not in repo_url:
            self.logger.warning(f"Non-GitHub repo, skipping: {repo_url}")
            return cve_data
        
        # 解析仓库信息
        try:
            repo_parts = repo_url.replace('https://github.com/', '').split('/')
            repo_owner, repo_name = repo_parts[0], repo_parts[1]
        except:
            self.logger.error(f"Failed to parse repo URL: {repo_url}")
            return cve_data
        
        versions = cve_data['versions']
        source = versions['source']
        target = versions['target']
        
        # 补全source内部的commits
        source_result = self._supplement_internal_commits(
            repo_owner, repo_name, cve_id, source, "source"
        )
        if not source_result:
            self.logger.error(f"❌ Source commits无法补全，跳过 {cve_id}")
            return None
        
        # 补全target内部的commits  
        target_result = self._supplement_internal_commits(
            repo_owner, repo_name, cve_id, target, "target"
        )
        if not target_result:
            self.logger.error(f"❌ Target commits无法补全，跳过 {cve_id}")
            return None
        
        self.logger.info(f"✅ {cve_id} commit补全成功")
        return cve_data
    
    def _supplement_internal_commits(self, repo_owner: str, repo_name: str, 
                                   cve_id: str, version_data: Dict, 
                                   version_type: str) -> bool:
        """
        在单个version(source/target)内部补全commits
        
        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称
            cve_id: CVE编号
            version_data: version数据 (source或target)
            version_type: 版本类型标识 ("source"或"target")
            
        Returns:
            bool: 是否成功补全(至少有一对有效的vulnerable+patched)
        """
        vulnerable_commit = version_data['vulnerable']['commit']
        patched_commit = version_data['patched']['commit']
        
        # 验证现有commits的有效性
        vul_valid = vulnerable_commit and self.validate_commit(vulnerable_commit)
        patch_valid = patched_commit and self.validate_commit(patched_commit)
        
        self.logger.info(f"📊 {version_type} commits状态: vul={vul_valid} patch={patch_valid}")
        
        # 情况1: 两个都有效，无需补全
        if vul_valid and patch_valid:
            self.logger.info(f"✅ {version_type} commits都有效，无需补全")
            return True
        
        # 情况2: 两个都无效，无法补全
        if not vul_valid and not patch_valid:
            self.logger.warning(f"❌ {version_type} 两个commits都无效，无法补全")
            return False
        
        # 情况3: vulnerable有效，patched无效 -> 查找修复commit
        if vul_valid and not patch_valid:
            self.logger.info(f"🔍 {version_type}: 用vulnerable查找patched commit")
            fix_commit = self.find_fix_commit_by_message(repo_owner, repo_name, cve_id, vulnerable_commit)
            if fix_commit:
                version_data['patched']['commit'] = fix_commit
                self.logger.info(f"✅ 补全{version_type} patched: {fix_commit[:8]}...")
                return True
            else:
                self.logger.warning(f"❌ 未找到{version_type}的修复commit")
                return False
        
        # 情况4: patched有效，vulnerable无效 -> 查找被修复的commit
        if not vul_valid and patch_valid:
            self.logger.info(f"🔍 {version_type}: 用patched查找vulnerable commit")
            vul_commit = self.find_vulnerable_commit_by_patch(repo_owner, repo_name, patched_commit)
            if vul_commit:
                version_data['vulnerable']['commit'] = vul_commit
                self.logger.info(f"✅ 补全{version_type} vulnerable: {vul_commit[:8]}...")
                return True
            else:
                self.logger.warning(f"❌ 未找到{version_type}的漏洞commit")
                return False
        
        return False
    
    def find_vulnerable_commit_by_patch(self, repo_owner: str, repo_name: str, 
                                      patch_commit: str) -> Optional[str]:
        """
        根据修复commit查找它修复的漏洞commit
        
        Args:
            repo_owner: 仓库所有者
            repo_name: 仓库名称
            patch_commit: 修复commit哈希
            
        Returns:
            Optional[str]: 找到的漏洞commit哈希
        """
        try:
            # 获取patch commit的详细信息
            commit_url = f"{self.github_api_base}/repos/{repo_owner}/{repo_name}/commits/{patch_commit}"
            response = requests.get(commit_url, headers=self.headers)
            
            if response.status_code != 200:
                return None
            
            commit_data = response.json()
            
            # 分析commit message，查找references
            message = commit_data['commit']['message'].lower()
            
            # 查找parent commits (通常漏洞在parent中)
            parents = commit_data.get('parents', [])
            for parent in parents:
                parent_sha = parent['sha']
                if self.validate_commit(parent_sha):
                    self.logger.info(f"Found potential vulnerable commit: {parent_sha}")
                    return parent_sha
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding vulnerable commit: {e}")
            return None


def main():
    """测试函数"""
    import json
    
    # 测试用例
    repo_path = "experiments/enhanced_dataset/shared_repos/linux_17fcd025b065"
    validator = CommitValidator(repo_path)
    
    # 加载测试数据
    with open('data/enhanced_data/enhanced_and_nvd_dataset.json') as f:
        data = json.load(f)
    
    # 测试第一个CVE
    test_cve = data[0]
    print(f"📊 测试CVE: {test_cve['cve_id']}")
    
    supplemented = validator.supplement_commit_data(test_cve)
    print("✅ 补全完成")


if __name__ == '__main__':
    main()