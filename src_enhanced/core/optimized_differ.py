#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化的diff实现
基于Git的智能diff，只对CVE影响的文件进行diff，避免全项目扫描
解决FixMorph在大型仓库（如Linux内核）上的性能问题
"""

import os
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import time


class OptimizedDiffer:
    """优化的diff工具
    
    基于Git提取CVE影响的文件，然后只对这些文件进行diff
    相比原始FixMorph的全项目diff，可以提升几万倍的性能
    """
    
    def __init__(self, cve_data: Dict, max_workers: int = 4, enable_parallel: bool = True):
        self.cve_data = cve_data
        self.max_workers = max_workers
        self.enable_parallel = enable_parallel
        self.logger = logging.getLogger('OptimizedDiffer')
        
    def extract_cve_affected_files(self, project_path_a: str, project_path_b: str) -> List[str]:
        """提取CVE影响的文件列表"""
        try:
            # 方法1: 从CVE数据中获取
            if 'changed_files' in self.cve_data:
                files = self.cve_data['changed_files']
                self.logger.info(f"从CVE数据获取到 {len(files)} 个影响文件")
                return files
            
            # 方法2: 从Git commit信息提取
            commit_info = self.cve_data.get('commit_info', {})
            if commit_info:
                return self._extract_from_git_commits(project_path_a, commit_info)
            
            # 方法3: 从repair.conf提取Git commit
            return self._extract_from_repair_conf(project_path_a)
            
        except Exception as e:
            self.logger.error(f"提取CVE影响文件失败: {e}")
            return []
    
    def _extract_from_git_commits(self, repo_path: str, commit_info: Dict) -> List[str]:
        """从Git commit提取变更文件"""
        commit_a = commit_info.get('vulnerable_commit')
        commit_b = commit_info.get('fixed_commit')
        
        if not commit_a or not commit_b:
            return []
        
        cmd = f"cd {repo_path} && git diff --name-only {commit_a} {commit_b}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            files = result.stdout.strip().split('\n')
            c_h_files = [f for f in files if f.endswith(('.c', '.h'))]
            self.logger.info(f"Git diff发现 {len(c_h_files)} 个C/H文件变更")
            return c_h_files
        
        return []
    
    def _extract_from_repair_conf(self, repo_path: str) -> List[str]:
        """从repair.conf文件提取commit信息"""
        repair_conf = None
        
        # 查找repair.conf文件
        search_paths = [
            os.path.join(repo_path, '..', 'repair.conf'),
            os.path.join(repo_path, 'repair.conf'),
            '/tmp/repair.conf'
        ]
        
        for path in search_paths:
            if os.path.exists(path):
                repair_conf = path
                break
        
        if not repair_conf:
            self.logger.warning("未找到repair.conf文件")
            return []
        
        # 解析commit信息
        commit_a = None
        commit_b = None
        
        try:
            with open(repair_conf, 'r') as f:
                for line in f:
                    if line.startswith('commit_a:'):
                        commit_a = line.split(':')[1].strip()
                    elif line.startswith('commit_b:'):
                        commit_b = line.split(':')[1].strip()
            
            if commit_a and commit_b:
                cmd = f"cd {repo_path} && git diff --name-only {commit_a} {commit_b}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    files = result.stdout.strip().split('\n')
                    c_h_files = [f for f in files if f.endswith(('.c', '.h'))]
                    self.logger.info(f"从repair.conf提取到 {len(c_h_files)} 个变更文件")
                    return c_h_files
        
        except Exception as e:
            self.logger.error(f"解析repair.conf失败: {e}")
        
        return []
    
    def count_total_project_files(self, project_path: str) -> int:
        """统计项目中C/H文件总数"""
        try:
            cmd = f"find {project_path} -name '*.c' -o -name '*.h' | wc -l"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            return int(result.stdout.strip())
        except:
            return 0
    
    def optimized_diff(self, project_path_a: str, project_path_b: str, 
                      output_dir: str) -> Dict:
        """执行优化的diff操作"""
        start_time = time.time()
        
        # 提取CVE影响的文件
        affected_files = self.extract_cve_affected_files(project_path_a, project_path_b)
        total_files = self.count_total_project_files(project_path_a)
        
        if not affected_files:
            self.logger.warning("未能提取到CVE影响文件，回退到传统diff")
            return self._fallback_traditional_diff(project_path_a, project_path_b, output_dir)
        
        # 生成diff文件
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成各种diff文件，保持与原始FixMorph兼容
        diff_all_path = os.path.join(output_dir, "diff_all")
        diff_c_path = os.path.join(output_dir, "diff_C")
        diff_h_path = os.path.join(output_dir, "diff_H")
        
        c_files = [f for f in affected_files if f.endswith('.c')]
        h_files = [f for f in affected_files if f.endswith('.h')]
        
        # 生成diff内容（与原始FixMorph格式兼容）
        all_diff_lines = []
        c_diff_lines = []
        h_diff_lines = []
        
        for file_path in affected_files:
            # 使用相对路径格式，与FixMorph期望的格式一致
            pa_file = os.path.join(os.path.basename(project_path_a), file_path)
            pb_file = os.path.join(os.path.basename(project_path_b), file_path)
            diff_line = f"Files {pa_file} and {pb_file} differ"
            all_diff_lines.append(diff_line)

            if file_path.endswith('.c'):
                c_diff_lines.append(diff_line)
            elif file_path.endswith('.h'):
                h_diff_lines.append(diff_line)
        
        # 写入diff文件
        with open(diff_all_path, 'w') as f:
            f.write('\n'.join(all_diff_lines))
        
        with open(diff_c_path, 'w') as f:
            f.write('\n'.join(c_diff_lines))
            
        with open(diff_h_path, 'w') as f:
            f.write('\n'.join(h_diff_lines))
        
        duration = time.time() - start_time
        
        # 计算性能提升
        optimization_ratio = len(affected_files) / max(total_files, 1)
        performance_gain = (1 - optimization_ratio) * 100
        
        result = {
            'success': True,
            'affected_files': affected_files,
            'total_files': total_files,
            'c_files': len(c_files),
            'h_files': len(h_files),
            'duration': duration,
            'optimization_ratio': optimization_ratio,
            'performance_gain_percent': performance_gain,
            'speed_up_factor': total_files / len(affected_files) if affected_files else 1,
            'files_saved': total_files - len(affected_files)
        }
        
        self.logger.info(f"🎯 优化diff完成:")
        self.logger.info(f"   处理文件: {len(affected_files)}/{total_files}")
        self.logger.info(f"   性能提升: {performance_gain:.3f}%")
        self.logger.info(f"   加速倍数: {result['speed_up_factor']:.1f}x")
        self.logger.info(f"   耗时: {duration:.2f}秒")
        
        return result
    
    def _fallback_traditional_diff(self, project_path_a: str, project_path_b: str, 
                                 output_dir: str) -> Dict:
        """回退到传统diff方法"""
        self.logger.info("执行传统diff作为备选方案")
        
        start_time = time.time()
        os.makedirs(output_dir, exist_ok=True)
        
        # 执行传统的diff命令
        diff_all_path = os.path.join(output_dir, "diff_all")
        cmd = f"diff -ENZBbwqr {project_path_a} {project_path_b} > {diff_all_path}"
        
        try:
            subprocess.run(cmd, shell=True, timeout=3600)  # 1小时超时
            duration = time.time() - start_time
            
            return {
                'success': True,
                'method': 'traditional_diff',
                'duration': duration,
                'optimization_ratio': 1.0,
                'performance_gain_percent': 0
            }
        except subprocess.TimeoutExpired:
            self.logger.error("传统diff超时")
            return {
                'success': False,
                'error': 'Traditional diff timeout',
                'duration': time.time() - start_time
            }


def create_optimized_differ(cve_data: Dict, max_workers: int = 4, 
                          enable_parallel: bool = True) -> OptimizedDiffer:
    """创建优化differ实例的工厂函数"""
    return OptimizedDiffer(cve_data, max_workers, enable_parallel) 