#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GitHub API 管理器 - 高效的仓库和commit管理
使用GitHub API替代git clone，实现按需下载和智能缓存
"""

import os
import requests
import json
import zipfile
import logging
from pathlib import Path
from typing import Dict, Optional, List
import hashlib
import time

class GitHubAPIManager:
    """GitHub API管理器 - 高效替代git clone"""
    
    def __init__(self, github_token: Optional[str] = None, cache_dir: str = "experiments/enhanced_dataset/api_cache"):
        # 支持多个GitHub token以避免速率限制
        self.github_tokens = [
            "*********************************************************************************************",
            "*********************************************************************************************"
        ]

        # 当前使用的token索引
        self.current_token_index = 0
        self.github_token = self.github_tokens[0] if self.github_tokens else github_token

        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.logger = self._setup_logger()

        # API配置
        self.api_base = "https://api.github.com"
        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'FixMorph-Enhanced/1.0'
        }

        # 速率限制跟踪
        self.requests_remaining = 5000 if self.github_token else 60
        self.rate_limit_reset = 0

        if self.github_token:
            self.headers['Authorization'] = f'token {self.github_token}'
            self.logger.info("🔐 GitHub API authenticated access enabled")
            self.logger.info(f"🔑 Active token: ...{self.github_token[-8:]}")
            self.logger.info(f"📊 Rate limit: 5000 requests/hour")
        else:
            self.logger.warning("⚠️ Using GitHub API without authentication (60 requests/hour limit)")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('GitHubAPIManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _parse_github_url(self, repo_url: str) -> tuple:
        """解析GitHub仓库URL"""
        # https://github.com/torvalds/linux.git -> torvalds, linux
        if 'github.com' not in repo_url:
            raise ValueError(f"Not a GitHub repository: {repo_url}")
        
        # 清理URL
        repo_url = repo_url.replace('.git', '')
        if repo_url.endswith('/'):
            repo_url = repo_url[:-1]
        
        parts = repo_url.replace('https://github.com/', '').split('/')
        if len(parts) != 2:
            raise ValueError(f"Invalid GitHub URL format: {repo_url}")
            
        return parts[0], parts[1]  # owner, repo
    
    def _get_commit_cache_path(self, owner: str, repo: str, commit_hash: str) -> Path:
        """获取commit缓存路径"""
        cache_name = f"{owner}_{repo}_{commit_hash[:8]}"
        return self.cache_dir / cache_name
    
    def get_commit_info(self, repo_url: str, commit_hash: str) -> Optional[Dict]:
        """通过API获取commit信息"""
        try:
            owner, repo = self._parse_github_url(repo_url)
            api_url = f"{self.api_base}/repos/{owner}/{repo}/commits/{commit_hash}"
            
            self.logger.info(f"📡 Fetching commit info: {owner}/{repo}@{commit_hash[:8]}")
            
            response = requests.get(api_url, headers=self.headers, timeout=30)
            
            if response.status_code == 200:
                commit_info = response.json()
                self.logger.info(f"✅ Got commit info for {commit_hash[:8]}")
                return commit_info
            elif response.status_code == 404:
                self.logger.error(f"❌ Commit not found: {commit_hash}")
                return None
            else:
                self.logger.error(f"❌ API error {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Failed to get commit info: {e}")
            return None
    
    def download_commit_archive(self, repo_url: str, commit_hash: str, target_dir: Path) -> bool:
        """下载特定commit的归档文件"""
        try:
            owner, repo = self._parse_github_url(repo_url)
            
            # 检查缓存
            cache_path = self._get_commit_cache_path(owner, repo, commit_hash)
            if cache_path.exists():
                self.logger.info(f"📦 Using cached archive: {cache_path}")
                return self._extract_cached_archive(cache_path, target_dir)
            
            # 下载归档
            archive_url = f"{self.api_base}/repos/{owner}/{repo}/zipball/{commit_hash}"
            
            self.logger.info(f"⬇️ Downloading archive: {owner}/{repo}@{commit_hash[:8]}")
            
            response = requests.get(archive_url, headers=self.headers, timeout=600, stream=True)
            
            if response.status_code != 200:
                self.logger.error(f"❌ Download failed {response.status_code}: {response.text}")
                return False
            
            # 保存到缓存
            cache_path.parent.mkdir(parents=True, exist_ok=True)
            archive_file = cache_path.with_suffix('.zip')
            
            with open(archive_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.logger.info(f"💾 Saved archive to cache: {archive_file}")
            
            # 解压到目标目录
            return self._extract_archive(archive_file, target_dir, cache_path)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to download archive: {e}")
            return False
    
    def _extract_archive(self, archive_file: Path, target_dir: Path, cache_path: Path) -> bool:
        """解压归档文件"""
        try:
            # 清理目标目录
            if target_dir.exists():
                import shutil
                shutil.rmtree(target_dir)
            
            # 解压
            with zipfile.ZipFile(archive_file, 'r') as zip_ref:
                zip_ref.extractall(cache_path)
            
            # 找到解压后的目录（GitHub归档包含一个子目录）
            extracted_dirs = [d for d in cache_path.iterdir() if d.is_dir()]
            if not extracted_dirs:
                self.logger.error("❌ No directory found in archive")
                return False
            
            source_dir = extracted_dirs[0]
            
            # 移动到目标位置
            import shutil
            shutil.move(str(source_dir), str(target_dir))

            # 修复权限问题（GitHub zip文件权限不正确）
            self._fix_permissions(target_dir)

            self.logger.info(f"✅ Extracted to: {target_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to extract archive: {e}")
            return False
    
    def _extract_cached_archive(self, cache_path: Path, target_dir: Path) -> bool:
        """使用缓存的归档"""
        try:
            # 如果缓存目录不存在，尝试重新下载
            if not cache_path.exists():
                self.logger.warning(f"❌ Cache path not found: {cache_path}")
                return False
                
            cached_dirs = [d for d in cache_path.iterdir() if d.is_dir()]
            if not cached_dirs:
                # 如果没有目录，可能需要重新解压zip文件
                zip_file = cache_path.with_suffix('.zip')
                if zip_file.exists():
                    self.logger.info(f"📦 Re-extracting cached zip: {zip_file}")
                    return self._extract_archive(zip_file, target_dir, cache_path)
                else:
                    self.logger.warning("❌ No cached directory or zip file found")
                    return False
            
            source_dir = cached_dirs[0]
            
            # 清理目标目录
            if target_dir.exists():
                import shutil
                shutil.rmtree(target_dir)
            
            # 复制缓存到目标位置
            import shutil
            shutil.copytree(source_dir, target_dir)

            # 修复权限问题（GitHub zip文件权限不正确）
            self._fix_permissions(target_dir)

            self.logger.info(f"✅ Used cached version: {target_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to use cached archive: {e}")
            return False
    
    def get_changed_files(self, repo_url: str, commit_hash: str) -> List[str]:
        """获取commit变更的文件列表"""
        commit_info = self.get_commit_info(repo_url, commit_hash)
        if not commit_info:
            return []
        
        changed_files = []
        if 'files' in commit_info:
            for file_info in commit_info['files']:
                if 'filename' in file_info:
                    changed_files.append(file_info['filename'])
        
        return changed_files
    
    def setup_cve_experiment_fast(self, cve_data: Dict, experiment_dir: Path) -> bool:
        """快速设置CVE实验（使用API）"""
        try:
            cve_id = cve_data.get('cve_id', 'unknown')
            repo_url = cve_data.get('source_repo')
            
            if not repo_url or 'github.com' not in repo_url:
                self.logger.error(f"❌ Not a GitHub repository: {repo_url}")
                return False
            
            self.logger.info(f"🚀 Setting up {cve_id} via GitHub API")
            
            # 创建实验目录
            experiment_dir.mkdir(parents=True, exist_ok=True)
            
            # 下载各个版本
            versions = [
                ('pa', cve_data.get('pa')),  # 源版本漏洞commit
                ('pb', cve_data.get('pb')),  # 源版本修复commit
                ('pc', cve_data.get('pc')),  # 目标版本漏洞commit
                ('pe', cve_data.get('pe')),  # 目标版本修复commit
            ]
            
            success_count = 0
            for version_name, commit_hash in versions:
                if not commit_hash:
                    continue
                
                version_dir = experiment_dir / version_name
                if self.download_commit_archive(repo_url, commit_hash, version_dir):
                    # 初始化Git仓库（FixMorph需要Git历史）
                    self._initialize_git_repo(version_dir, commit_hash, version_name)
                    success_count += 1
                    self.logger.info(f"✅ {version_name}: {commit_hash[:8]}")
                else:
                    self.logger.error(f"❌ Failed to download {version_name}: {commit_hash[:8]}")
            
            if success_count >= 3:
                self.logger.info(f"🎉 Successfully set up {success_count} versions for {cve_id}")
                return True
            else:
                self.logger.error(f"❌ Only {success_count} versions downloaded for {cve_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to setup experiment via API: {e}")
            return False
    
    def get_rate_limit(self) -> Dict:
        """获取API速率限制信息"""
        try:
            response = requests.get(f"{self.api_base}/rate_limit", headers=self.headers, timeout=10)
            if response.status_code == 200:
                return response.json()
        except Exception as e:
            self.logger.warning(f"Failed to get rate limit: {e}")
        
        return {}
    
    def wait_for_rate_limit(self):
        """等待速率限制重置"""
        rate_info = self.get_rate_limit()
        if 'rate' in rate_info:
            remaining = rate_info['rate'].get('remaining', 0)
            if remaining < 10:
                reset_time = rate_info['rate'].get('reset', 0)
                wait_time = reset_time - int(time.time()) + 60  # 额外等待1分钟
                if wait_time > 0:
                    self.logger.warning(f"⏰ Rate limit low, waiting {wait_time} seconds...")
                    time.sleep(wait_time)

    def _switch_to_next_token(self) -> bool:
        """切换到下一个可用的token"""
        if len(self.github_tokens) <= 1:
            return False

        # 尝试下一个token
        self.current_token_index = (self.current_token_index + 1) % len(self.github_tokens)
        new_token = self.github_tokens[self.current_token_index]

        if new_token != self.github_token:
            self.github_token = new_token
            self.headers['Authorization'] = f'token {self.github_token}'
            self.requests_remaining = 5000  # 重置速率限制

            self.logger.info(f"🔄 Switched to token: ...{self.github_token[-8:]}")
            return True

        return False

    def _update_rate_limit_info(self, response):
        """从响应头更新速率限制信息"""
        try:
            if 'X-RateLimit-Remaining' in response.headers:
                self.requests_remaining = int(response.headers['X-RateLimit-Remaining'])
            if 'X-RateLimit-Reset' in response.headers:
                self.rate_limit_reset = int(response.headers['X-RateLimit-Reset'])

            # 如果速率限制很低，尝试切换token
            if self.requests_remaining < 10:
                self.logger.warning(f"⚠️ Rate limit low: {self.requests_remaining} requests remaining")
                if self._switch_to_next_token():
                    self.logger.info("✅ Successfully switched to next token")

        except (ValueError, KeyError) as e:
            self.logger.debug(f"Failed to parse rate limit headers: {e}")

    def validate_commits_exist(self, repo_url: str, commits: dict) -> dict:
        """验证所有commit是否存在于仓库中

        Args:
            repo_url: 仓库URL
            commits: commit字典，如 {'pa': 'commit_hash', 'pb': 'commit_hash', ...}

        Returns:
            dict: 验证结果，如 {'pa': True, 'pb': False, ...}
        """
        try:
            # 解析仓库信息
            repo_info = self._parse_github_url(repo_url)
            if not repo_info:
                self.logger.error(f"❌ Invalid GitHub URL: {repo_url}")
                return {version: False for version in commits.keys()}

            owner, repo = repo_info
            results = {}

            self.logger.info(f"🔍 Validating {len(commits)} commits in {owner}/{repo}")

            for version, commit_hash in commits.items():
                if not commit_hash:
                    results[version] = False
                    self.logger.warning(f"⚠️ {version}: Empty commit hash")
                    continue

                # 使用GitHub API检查commit是否存在
                commit_url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_hash}"

                try:
                    response = requests.get(commit_url, headers=self.headers, timeout=10)

                    if response.status_code == 200:
                        results[version] = True
                        self.logger.info(f"✅ {version}: {commit_hash[:8]} exists")
                    elif response.status_code == 404:
                        results[version] = False
                        self.logger.error(f"❌ {version}: {commit_hash[:8]} not found")
                    else:
                        results[version] = False
                        self.logger.error(f"❌ {version}: API error {response.status_code}")

                    # 更新速率限制信息
                    self._update_rate_limit_info(response)

                except requests.RequestException as e:
                    results[version] = False
                    self.logger.error(f"❌ {version}: Request failed - {e}")

                # 检查速率限制
                if self.requests_remaining <= 1:
                    self.logger.warning("⚠️ GitHub API rate limit nearly exhausted")
                    break

            # 总结验证结果
            valid_count = sum(1 for valid in results.values() if valid)
            total_count = len(results)

            if valid_count == total_count:
                self.logger.info(f"✅ All {total_count} commits validated successfully")
            else:
                invalid_count = total_count - valid_count
                self.logger.error(f"❌ {invalid_count}/{total_count} commits are invalid")

            return results

        except Exception as e:
            self.logger.error(f"❌ Failed to validate commits: {e}")
            return {version: False for version in commits.keys()}

    def _fix_permissions(self, directory: Path):
        """修复GitHub zip文件的权限问题"""
        try:
            import stat

            # 递归修复目录和文件权限
            for root, dirs, files in os.walk(directory):
                # 修复目录权限
                for dir_name in dirs:
                    dir_path = os.path.join(root, dir_name)
                    os.chmod(dir_path, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)

                # 修复文件权限
                for file_name in files:
                    file_path = os.path.join(root, file_name)
                    # 检查文件是否应该是可执行的
                    if file_name.endswith(('.sh', '.py', '.pl')) or 'configure' in file_name:
                        # 可执行文件
                        os.chmod(file_path, stat.S_IRWXU | stat.S_IRGRP | stat.S_IXGRP | stat.S_IROTH | stat.S_IXOTH)
                    else:
                        # 普通文件
                        os.chmod(file_path, stat.S_IRUSR | stat.S_IWUSR | stat.S_IRGRP | stat.S_IROTH)

            self.logger.debug(f"🔧 Fixed permissions for: {directory}")

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to fix permissions for {directory}: {e}")

    def _initialize_git_repo(self, repo_dir: Path, commit_hash: str, version_name: str):
        """为下载的源码初始化Git仓库（FixMorph需要Git历史）"""
        try:
            import subprocess
            from datetime import datetime

            # 切换到目录
            original_cwd = os.getcwd()
            os.chdir(str(repo_dir))

            # 初始化Git仓库
            subprocess.run(['git', 'init'], check=True, capture_output=True)

            # 配置Git用户（避免警告）
            subprocess.run(['git', 'config', 'user.name', 'FixMorph'], check=True, capture_output=True)
            subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], check=True, capture_output=True)

            # 添加所有文件
            subprocess.run(['git', 'add', '.'], check=True, capture_output=True)

            # 创建真实的commit，使用原始commit hash作为commit message
            commit_message = f"{version_name}: {commit_hash}"

            # 设置commit的环境变量，使其看起来更真实
            env = os.environ.copy()
            env['GIT_AUTHOR_DATE'] = datetime.now().isoformat()
            env['GIT_COMMITTER_DATE'] = datetime.now().isoformat()

            result = subprocess.run(
                ['git', 'commit', '-m', commit_message],
                check=True,
                capture_output=True,
                env=env
            )

            # 获取新创建的commit hash
            result = subprocess.run(['git', 'rev-parse', 'HEAD'], check=True, capture_output=True, text=True)
            new_commit_hash = result.stdout.strip()

            # 创建一个tag指向这个commit，使用原始commit hash作为tag名
            tag_name = f"original-{commit_hash[:8]}"
            subprocess.run(['git', 'tag', tag_name], check=True, capture_output=True)

            # 恢复原始工作目录
            os.chdir(original_cwd)

            self.logger.debug(f"🔧 Initialized Git repo for {version_name} (commit: {new_commit_hash[:8]}, tag: {tag_name})")

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to initialize Git repo for {version_name}: {e}")
            # 恢复原始工作目录
            try:
                os.chdir(original_cwd)
            except:
                pass