#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FixMorph配置管理模块

统一管理所有路径配置，支持相对路径和绝对路径
"""

import os
import sys
from pathlib import Path
from typing import Optional

# 获取项目根目录（FixMorph项目的根目录）
def get_project_root() -> Path:
    """获取项目根目录"""
    # 从当前文件位置向上查找，直到找到包含FixMorph.py的目录
    current_path = Path(__file__).resolve()
    
    # 向上查找直到找到项目根目录
    for parent in [current_path] + list(current_path.parents):
        if (parent / "FixMorph.py").exists():
            return parent
        if (parent / "setup.py").exists() and (parent / "app").exists():
            return parent
    
    # 如果找不到，使用当前工作目录的上级目录
    return Path.cwd()

# 项目根目录
PROJECT_ROOT = get_project_root()

# 数据目录配置
DATA_DIR = PROJECT_ROOT / "data"
ENHANCED_DATA_DIR = DATA_DIR / "enhanced_data"
ENHANCED_DATASET_FILE = ENHANCED_DATA_DIR / "enhanced_and_nvd_dataset.json"

# 实验目录配置
EXPERIMENTS_DIR = PROJECT_ROOT / "experiments"
ENHANCED_DATASET_DIR = EXPERIMENTS_DIR / "enhanced_dataset"
REAL_EXPERIMENTS_DIR = EXPERIMENTS_DIR / "real_experiments"
TEST_SAMPLE_DIR = EXPERIMENTS_DIR / "test_sample"

# 具体子目录
SHARED_REPOS_DIR = ENHANCED_DATASET_DIR / "shared_repos"
ACTIVE_EXPERIMENTS_DIR = ENHANCED_DATASET_DIR / "active_experiments"
RESULTS_DIR = ENHANCED_DATASET_DIR / "results"
CHECKPOINTS_DIR = ENHANCED_DATASET_DIR / "checkpoints"
API_CACHE_DIR = ENHANCED_DATASET_DIR / "api_cache"

# 数据文件
CONVERTED_DATA_FILE = ENHANCED_DATASET_DIR / "converted_data.json"
PERFORMANCE_REPORT_FILE = EXPERIMENTS_DIR / "performance_analysis_report.json"
PERFORMANCE_SUMMARY_FILE = EXPERIMENTS_DIR / "performance_summary.txt"

# 输出目录
OUTPUT_DIR = PROJECT_ROOT / "output"

# 确保关键目录存在
def ensure_directories():
    """确保所有关键目录存在"""
    dirs_to_create = [
        DATA_DIR,
        ENHANCED_DATA_DIR,
        EXPERIMENTS_DIR,
        ENHANCED_DATASET_DIR,
        REAL_EXPERIMENTS_DIR,
        TEST_SAMPLE_DIR,
        SHARED_REPOS_DIR,
        ACTIVE_EXPERIMENTS_DIR,
        RESULTS_DIR,
        CHECKPOINTS_DIR,
        API_CACHE_DIR,
        OUTPUT_DIR
    ]
    
    for dir_path in dirs_to_create:
        dir_path.mkdir(parents=True, exist_ok=True)

# GitHub Token管理
def get_github_token() -> Optional[str]:
    """获取GitHub Token"""
    # 优先级：环境变量 > 配置文件
    token = os.environ.get('GITHUB_TOKEN')
    
    if not token:
        token_file = PROJECT_ROOT / ".github_token"
        if token_file.exists():
            try:
                token = token_file.read_text().strip()
            except Exception:
                pass
    
    return token

# 路径转换函数
def to_str(path) -> str:
    """将Path对象转换为字符串"""
    return str(path)

# 兼容性函数：将绝对路径转换为相对路径
def convert_legacy_path(legacy_path: str) -> str:
    """转换旧的硬编码路径为新的相对路径"""
    if legacy_path.startswith("/FixMorph/"):
        relative_part = legacy_path[10:]  # 移除 "/FixMorph/"
        return str(PROJECT_ROOT / relative_part)
    return legacy_path

# 导出常用路径的字符串版本（向后兼容）
PROJECT_ROOT_STR = to_str(PROJECT_ROOT)
ENHANCED_DATASET_FILE_STR = to_str(ENHANCED_DATASET_FILE)
ENHANCED_DATASET_DIR_STR = to_str(ENHANCED_DATASET_DIR)
CONVERTED_DATA_FILE_STR = to_str(CONVERTED_DATA_FILE)
SHARED_REPOS_DIR_STR = to_str(SHARED_REPOS_DIR)
CHECKPOINTS_DIR_STR = to_str(CHECKPOINTS_DIR)
API_CACHE_DIR_STR = to_str(API_CACHE_DIR)

if __name__ == "__main__":
    print("🔧 FixMorph路径配置")
    print("=" * 50)
    print(f"项目根目录: {PROJECT_ROOT}")
    print(f"数据目录: {ENHANCED_DATASET_FILE}")
    print(f"实验目录: {ENHANCED_DATASET_DIR}")
    print(f"转换数据: {CONVERTED_DATA_FILE}")
    print("=" * 50)
    
    # 确保目录存在
    ensure_directories()
    print("✅ 所有目录已创建") 