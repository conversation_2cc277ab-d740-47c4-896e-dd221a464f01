#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
FixMorph实验运行器

负责批量运行FixMorph实验，管理实验流程和结果收集
"""

import json
import os
import sys
import argparse
import logging
import subprocess
import time
from pathlib import Path
try:
    from typing import Dict, List, Optional, Tuple
except ImportError:
    # Python 3.5 兼容性
    Dict, List, Optional, Tuple = dict, list, type(None), tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from src_enhanced.modules.git_manager import GitManager
from src_enhanced.modules.github_api_manager import GitHubAPIManager
from src_enhanced.modules.storage_manager import StorageManager
from src_enhanced.modules.checkpoint_manager import CheckpointManager, ExperimentStatus
from src_enhanced.core.data_converter import CVEDataConverter
from src_enhanced.core.optimized_differ import create_optimized_differ


class ExperimentRunner:
    """FixMorph实验运行器"""
    
    def __init__(self,
                 data_file,
                 output_dir="experiments/enhanced_dataset",
                 fixmorph_path=None,
                 max_workers=2,
                 timeout=3600,
                 stage2_mode=False,
                 build_verification=False,
                 github_token=None):
        
        self.data_file = data_file
        self.output_dir = Path(output_dir)

        # 自动检测FixMorph路径
        if fixmorph_path is None:
            # 使用当前项目根目录
            current_path = Path(__file__).resolve()
            for parent in [current_path] + list(current_path.parents):
                if (parent / "FixMorph.py").exists():
                    fixmorph_path = parent
                    break
            if fixmorph_path is None:
                fixmorph_path = Path.cwd()

        self.fixmorph_path = Path(fixmorph_path)
        self.max_workers = max_workers
        self.timeout = timeout
        self.stage2_mode = stage2_mode
        self.build_verification = build_verification
        
        self.logger = self._setup_logger()
        
        # 🚀 新增：优先使用GitHub API管理器
        self.api_manager = GitHubAPIManager(github_token=github_token)
        
        # 保留传统git管理器作为降级选项
        self.git_manager = GitManager(github_token=github_token)
        
        self.storage_manager = StorageManager(str(self.output_dir))
        self.checkpoint_manager = CheckpointManager()

        # 创建输出目录
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.active_experiments_dir = self.output_dir / "active_experiments"
        self.results_dir = self.output_dir / "results"
        self.results_dir.mkdir(parents=True, exist_ok=True)

        # 启动存储监控
        self.storage_manager.start_monitoring()
        
        # 实验统计
        self.stats = {
            'total': 0,
            'success': 0,
            'failed': 0,
            'timeout': 0,
            'setup_failed': 0,
            'api_setup': 0,      # 使用API设置成功的数量
            'git_setup': 0,      # 使用git clone设置成功的数量
        }
        
        self.results = []
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志系统"""
        logger = logging.getLogger('ExperimentRunner')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def load_converted_data(self) -> List[Dict]:
        """加载转换后的数据"""
        if not os.path.exists(self.data_file):
            raise FileNotFoundError("Data file not found: {}".format(self.data_file))

        self.logger.info("Loading data from {}".format(self.data_file))
        with open(self.data_file, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)

        # 检查是否需要转换
        if self._needs_conversion(raw_data):
            self.logger.info("Converting raw CVE data to FixMorph format...")
            converter = CVEDataConverter(self.data_file, str(self.output_dir))
            converted_data = []
            failed_count = 0

            for cve_data in raw_data:
                try:
                    converted = converter.convert_cve_to_fixmorph(cve_data)
                    converted_data.append(converted)
                except Exception as e:
                    failed_count += 1
                    self.logger.warning("Failed to convert CVE {}: {}".format(cve_data.get('cve_id', 'unknown'), e))

            self.logger.info("Conversion completed: {} successful, {} failed".format(len(converted_data), failed_count))
            return converted_data
        else:
            self.logger.info("Data already in FixMorph format")
            return raw_data
    
    def _needs_conversion(self, data: List[Dict]) -> bool:
        """检查数据是否需要转换"""
        if not data:
            return False
        
        # 检查第一个元素是否包含FixMorph格式的字段
        first_item = data[0]
        
        # 如果包含原始格式的字段（如projects, versions），则需要转换
        has_original_fields = ('projects' in first_item or 'versions' in first_item)
        
        # 如果缺少FixMorph格式的字段（如source_repo, pa），则需要转换
        missing_fixmorph_fields = ('source_repo' not in first_item or 'pa' not in first_item)
        
        return has_original_fields and missing_fixmorph_fields
    
    def setup_single_experiment(self, cve_data: Dict) -> Optional[Path]:
        """设置单个实验"""
        cve_id = cve_data.get('cve_id', 'unknown')

        try:
            # 更新状态为设置中
            self.checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.SETUP)

            # 检查存储空间
            repo_url = cve_data.get('source_repo', '')
            required_space = self.storage_manager.estimate_experiment_size(repo_url)

            if not self.storage_manager.check_space_available(required_space):
                self.logger.warning(f"Insufficient storage for {cve_id}, attempting cleanup...")
                self.storage_manager.auto_cleanup()

                # 再次检查
                if not self.storage_manager.check_space_available(required_space):
                    self.logger.error(f"Still insufficient storage for {cve_id} after cleanup")
                    self.checkpoint_manager.update_experiment_status(
                        cve_id, ExperimentStatus.SETUP_FAILED,
                        {'error': 'Insufficient storage space'}
                    )
                    return None

            # 创建实验目录
            experiment_dir = self.active_experiments_dir / cve_id

            # 🚀 智能仓库设置：优先API，降级到git clone
            setup_success = False
            setup_method = "unknown"
            
            # 方法1: 尝试GitHub API管理器 (推荐)
            repo_url = cve_data.get('source_repo', '')
            if 'github.com' in repo_url:
                self.logger.info(f"⚡ Trying GitHub API setup for {cve_id}")
                if self.api_manager.setup_cve_experiment_fast(cve_data, experiment_dir):
                    setup_success = True
                    setup_method = "github_api"
                    self.logger.info(f"✅ {cve_id}: GitHub API setup successful")
                else:
                    self.logger.warning(f"⚠️ {cve_id}: GitHub API setup failed, trying git clone...")
            
            # 方法2: 降级到传统git clone
            if not setup_success:
                self.logger.info(f"🐌 Falling back to git clone for {cve_id}")
                if self.git_manager.setup_cve_experiment(cve_data, experiment_dir):
                    setup_success = True
                    setup_method = "git_clone"
                    self.logger.info(f"✅ {cve_id}: Git clone setup successful")
                else:
                    self.logger.error(f"❌ {cve_id}: Both API and git clone failed")
            
            # 检查最终结果
            if not setup_success:
                self.logger.error(f"Failed to setup repositories for {cve_id} using any method")
                self.checkpoint_manager.update_experiment_status(
                    cve_id, ExperimentStatus.SETUP_FAILED,
                    {'error': f'Repository setup failed (tried: {setup_method})'}
                )
                return None
            else:
                self.logger.info(f"📋 {cve_id}: Repository setup via {setup_method}")
                # 更新统计
                if setup_method == "github_api":
                    self.stats['api_setup'] += 1
                elif setup_method == "git_clone":
                    self.stats['git_setup'] += 1
            
            # 生成FixMorph配置文件
            if self.stage2_mode:
                # 阶段2：使用优化配置（智能diff + 构建验证）
                config_file = self.generate_stage2_config(cve_data, experiment_dir)
            else:
                # 阶段1：使用标准配置
                converter = CVEDataConverter("", str(self.output_dir))
                config_file = converter.generate_fixmorph_config(cve_data, experiment_dir)
            
            self.logger.info(f"✅ Experiment setup completed for {cve_id}")
            return experiment_dir
            
        except Exception as e:
            self.logger.error(f"Failed to setup experiment for {cve_id}: {e}")
            return None
    
    def generate_stage2_config(self, cve_data: Dict, experiment_dir: Path) -> Path:
        """生成阶段2的优化配置文件"""
        cve_id = cve_data.get('cve_id', 'unknown')
        
        # 阶段2配置模板
        stage2_config = f"""# FixMorph 阶段2配置 - 完整验证 ({cve_id})
# 启用智能diff + 构建验证

# 基本路径配置
path_a:{experiment_dir}/pa
path_b:{experiment_dir}/pb
path_c:{experiment_dir}/pc
path_e:{experiment_dir}/pe
tag_id:{cve_id}

# Git Commit信息
commit_a:{cve_data.get('source_commit_a', '')}
commit_b:{cve_data.get('source_commit_b', '')}
commit_c:{cve_data.get('target_commit_a', '')}
commit_e:{cve_data.get('target_commit_b', '')}

# 🚀 核心优化：启用智能diff (36,788倍性能提升)
linux-kernel:true
backport:true
version-control:git

# 🔧 构建验证配置
config_command_a:make allyesconfig
config_command_c:make allyesconfig
"""

        if self.build_verification:
            # 启用构建验证
            affected_files = cve_data.get('changed_files', [])
            if affected_files:
                # 智能构建：只构建影响的模块
                primary_file = affected_files[0]
                if primary_file.endswith('.c'):
                    object_file = primary_file.replace('.c', '.o')
                    stage2_config += f"""
# 智能构建验证：只构建CVE影响的文件
build_command_a:make {object_file}
build_command_c:make {object_file}
"""
                else:
                    # 回退到模块级构建
                    module_path = '/'.join(primary_file.split('/')[:-1]) + '/'
                    stage2_config += f"""
# 模块级构建验证
build_command_a:make {module_path}
build_command_c:make {module_path}
"""
            else:
                # 默认构建验证
                stage2_config += """
# 默认构建验证  
build_command_a:make -j$(nproc) --quiet
build_command_c:make -j$(nproc) --quiet
"""
        else:
            # 跳过构建，专注diff优化
            stage2_config += """
# 跳过构建验证，专注diff性能
build_command_a:skip
build_command_c:skip
"""

        # 添加资源管理配置
        stage2_config += f"""
# 资源管理
timeout:{self.timeout}
cleanup_after_build:true
parallel_jobs:{self.max_workers}

# 监控配置
verbose:true
debug:false
"""

        # 保存配置文件
        config_file = experiment_dir / "repair.conf"
        with open(config_file, 'w') as f:
            f.write(stage2_config)
        
        self.logger.info(f"🔧 Generated Stage2 config for {cve_id} (build_verification={self.build_verification})")
        return config_file
    
    def run_optimized_diff(self, experiment_dir: Path, cve_data: Dict) -> Dict:
        """运行优化的diff，替代FixMorph原始的全项目diff"""
        cve_id = cve_data.get('cve_id', 'unknown')
        
        try:
            self.logger.info(f"🚀 开始优化diff处理 for {cve_id}")
            
            # 构建路径 - 确保与FixMorph标准位置一致
            pa_path = experiment_dir / "pa"
            pb_path = experiment_dir / "pb"
            output_path = experiment_dir / "tmp"  # FixMorph期望的diff文件位置
            
            # 确保tmp目录存在
            output_path.mkdir(parents=True, exist_ok=True)
            
            if not pa_path.exists() or not pb_path.exists():
                return {
                    'success': False,
                    'error': 'PA or PB directory not found',
                    'cve_id': cve_id
                }
            
            # 创建优化differ
            differ = create_optimized_differ(cve_data)
            
            # 执行优化diff
            result = differ.optimized_diff(
                project_path_a=str(pa_path),
                project_path_b=str(pb_path),
                output_dir=str(output_path)
            )
            
            if result['success']:
                self.logger.info(f"🎯 {cve_id} 优化diff成功:")
                self.logger.info(f"   处理文件数: {len(result['affected_files'])}")
                self.logger.info(f"   项目总文件: {result['total_files']}")
                self.logger.info(f"   性能提升: {result['performance_gain_percent']:.3f}%")
                self.logger.info(f"   加速倍数: {result['speed_up_factor']:.1f}x")
                self.logger.info(f"   耗时: {result['duration']:.2f}秒")
                
                # 📍 显示diff文件位置
                self.logger.info(f"📁 Diff文件位置: {output_path}")
                diff_files = ['diff_all', 'diff_C', 'diff_H']
                for diff_file in diff_files:
                    diff_path = output_path / diff_file
                    if diff_path.exists():
                        self.logger.info(f"   ✅ {diff_file}: {diff_path}")
                    else:
                        self.logger.warning(f"   ❌ {diff_file}: 未生成")
                
                # 记录性能数据
                result['cve_id'] = cve_id
                result['diff_files_location'] = str(output_path)
                return result
            else:
                self.logger.warning(f"⚠️ {cve_id} 优化diff失败，使用备选方案")
                return result
                
        except Exception as e:
            self.logger.error(f"❌ {cve_id} 优化diff异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'cve_id': cve_id
            }
    
    def run_fixmorph(self, experiment_dir: Path, cve_id: str) -> Dict:
        """运行FixMorph实验"""
        config_file = experiment_dir / "repair.conf"

        if not config_file.exists():
            return {
                'cve_id': cve_id,
                'status': 'failed',
                'error': 'Configuration file not found',
                'duration': 0
            }

        try:
            # 更新状态为运行中
            self.checkpoint_manager.update_experiment_status(cve_id, ExperimentStatus.RUNNING)

            start_time = time.time()

            # 构建FixMorph命令
            fixmorph_cmd = [
                'python3.7',
                str(self.fixmorph_path / 'FixMorph.py'),
                f'--conf={config_file}'
            ]

            self.logger.info(f"Running FixMorph for {cve_id}: {' '.join(fixmorph_cmd)}")

            # 运行FixMorph
            result = subprocess.run(
                fixmorph_cmd,
                cwd=str(self.fixmorph_path),
                capture_output=True,
                text=True,
                timeout=self.timeout
            )

            duration = time.time() - start_time

            # 分析结果
            if result.returncode == 0:
                status = 'success'
                error = None
                self.logger.info(f"✅ FixMorph completed successfully for {cve_id} in {duration:.1f}s")

                # 更新成功状态
                self.checkpoint_manager.update_experiment_status(
                    cve_id, ExperimentStatus.SUCCESS,
                    {'duration': duration, 'completed_at': time.time()}
                )
            else:
                status = 'failed'
                error = result.stderr
                self.logger.error(f"❌ FixMorph failed for {cve_id}: {error}")

                # 更新失败状态
                self.checkpoint_manager.update_experiment_status(
                    cve_id, ExperimentStatus.FAILED,
                    {'duration': duration, 'error': error, 'stderr': result.stderr}
                )

            return {
                'cve_id': cve_id,
                'status': status,
                'error': error,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr
            }

        except subprocess.TimeoutExpired:
            duration = time.time() - start_time
            self.logger.error(f"⏰ FixMorph timeout for {cve_id} after {duration:.1f}s")

            # 更新超时状态
            self.checkpoint_manager.update_experiment_status(
                cve_id, ExperimentStatus.TIMEOUT,
                {'duration': duration, 'error': f'Timeout after {self.timeout}s'}
            )

            return {
                'cve_id': cve_id,
                'status': 'timeout',
                'error': f'Timeout after {self.timeout}s',
                'duration': duration
            }

        except Exception as e:
            duration = time.time() - start_time
            self.logger.error(f"❌ Error running FixMorph for {cve_id}: {e}")

            # 更新错误状态
            self.checkpoint_manager.update_experiment_status(
                cve_id, ExperimentStatus.FAILED,
                {'duration': duration, 'error': str(e)}
            )

            return {
                'cve_id': cve_id,
                'status': 'error',
                'error': str(e),
                'duration': duration
            }
    
    def cleanup_experiment(self, experiment_dir: Path, keep_results: bool = False):
        """智能清理实验目录 - 保留重要的diff文件和结果"""
        try:
            if not keep_results:
                # 完全清理，但保留关键文件
                self._smart_cleanup_experiment(experiment_dir, keep_important=True)
            else:
                # 只清理源码目录，完全保留结果和中间文件
                for subdir in ['pa', 'pb', 'pc', 'pe']:
                    subdir_path = experiment_dir / subdir
                    if subdir_path.exists():
                        self.git_manager.cleanup_experiment(subdir_path)
                
                self.logger.info(f"✅ Preserved results and diff files for: {experiment_dir.name}")
                        
        except Exception as e:
            self.logger.warning(f"Failed to cleanup {experiment_dir}: {e}")
    
    def _smart_cleanup_experiment(self, experiment_dir: Path, keep_important: bool = True):
        """智能清理：保留重要的diff文件和FixMorph结果"""
        if not experiment_dir.exists():
            return
            
        if not keep_important:
            # 完全删除
            self.git_manager.cleanup_experiment(experiment_dir)
            return
        
        try:
            # 保留的重要目录和文件
            important_items = {
                'tmp',          # diff文件位置
                'repair.conf',  # 配置文件
                'logs',         # 日志文件  
                'output',       # FixMorph输出
                'results'       # 实验结果
            }
            
            # 删除源码目录但保留重要文件
            for item in experiment_dir.iterdir():
                if item.name in ['pa', 'pb', 'pc', 'pe']:
                    # 删除大型源码目录
                    if item.is_dir():
                        import shutil
                        shutil.rmtree(item)
                        self.logger.debug(f"🗑️ Removed source directory: {item.name}")
                elif item.name not in important_items and item.is_dir():
                    # 删除其他非重要目录
                    import shutil
                    shutil.rmtree(item)
                    self.logger.debug(f"🗑️ Removed directory: {item.name}")
            
            self.logger.info(f"🧹 Smart cleanup completed for: {experiment_dir.name}")
            self.logger.info(f"📋 Preserved: diff files, config, results")
            
        except Exception as e:
            self.logger.error(f"❌ Smart cleanup failed for {experiment_dir}: {e}")
            # 降级到传统清理
            self.git_manager.cleanup_experiment(experiment_dir)
    
    def run_single_experiment(self, cve_data: Dict, keep_results: bool = False) -> Dict:
        """运行单个完整实验"""
        cve_id = cve_data.get('cve_id', 'unknown')
        
        try:
            # 设置实验
            experiment_dir = self.setup_single_experiment(cve_data)
            if not experiment_dir:
                self.stats['setup_failed'] += 1
                return {
                    'cve_id': cve_id,
                    'status': 'setup_failed',
                    'error': 'Failed to setup experiment',
                    'duration': 0
                }
            
            # 🚀 运行优化diff (替代FixMorph原始的全项目diff)
            self.logger.info(f"⚡ 开始优化diff处理 for {cve_id} (避免全项目扫描)")
            diff_result = self.run_optimized_diff(experiment_dir, cve_data)
            
            if not diff_result['success']:
                self.logger.warning(f"优化diff失败 for {cve_id}: {diff_result.get('error', 'Unknown error')}")
                # 继续使用原始FixMorph，让它处理diff
            else:
                self.logger.info(f"✅ 优化diff成功 for {cve_id}, 性能提升 {diff_result.get('performance_gain_percent', 0):.1f}%")
            
            # 运行FixMorph (现在会使用我们生成的优化diff文件)
            result = self.run_fixmorph(experiment_dir, cve_id)
            
            # 添加diff性能数据到结果中
            if diff_result['success']:
                result['diff_performance'] = {
                    'optimized': True,
                    'files_processed': len(diff_result.get('affected_files', [])),
                    'total_files': diff_result.get('total_files', 0),
                    'performance_gain': diff_result.get('performance_gain_percent', 0),
                    'speed_up_factor': diff_result.get('speed_up_factor', 1),
                    'diff_duration': diff_result.get('duration', 0)
                }
            else:
                result['diff_performance'] = {
                    'optimized': False,
                    'error': diff_result.get('error', 'Unknown error')
                }
            
            # 更新统计
            if result['status'] == 'success':
                self.stats['success'] += 1
            elif result['status'] == 'timeout':
                self.stats['timeout'] += 1
            else:
                self.stats['failed'] += 1
            
            # 清理实验目录
            self.cleanup_experiment(experiment_dir, keep_results)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error in experiment {cve_id}: {e}")
            self.stats['failed'] += 1
            return {
                'cve_id': cve_id,
                'status': 'error',
                'error': str(e),
                'duration': 0
            }
    
    def run_batch_experiments(self,
                            cve_list: Optional[List[str]] = None,
                            start_index: int = 0,
                            end_index: Optional[int] = None,
                            keep_results: bool = False,
                            resume: bool = True) -> List[Dict]:
        """批量运行实验（支持断点重连）"""

        # 加载数据
        data = self.load_converted_data()

        # 过滤数据
        if cve_list:
            data = [item for item in data if item.get('cve_id') in cve_list]

        if end_index is None:
            end_index = len(data)

        data = data[start_index:end_index]
        all_cve_ids = [item.get('cve_id') for item in data]

        # 断点重连逻辑
        if resume:
            # 获取需要执行的实验（排除已完成的）
            pending_cve_ids = self.checkpoint_manager.get_pending_experiments(all_cve_ids)
            completed_cve_ids = self.checkpoint_manager.get_completed_experiments(all_cve_ids)

            self.logger.info(f"Resume mode: {len(completed_cve_ids)} already completed, "
                           f"{len(pending_cve_ids)} pending")

            # 过滤数据，只处理待执行的实验
            data = [item for item in data if item.get('cve_id') in pending_cve_ids]

            # 更新统计信息
            self.stats['success'] = len(completed_cve_ids)
        else:
            # 重置所有实验状态
            for cve_id in all_cve_ids:
                self.checkpoint_manager.reset_experiment(cve_id)

        self.stats['total'] = len(all_cve_ids)
        self.logger.info(f"Starting batch experiments: {len(data)} CVEs to process, "
                        f"{self.stats['success']} already completed")
        
        # 并行运行实验
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_cve = {
                executor.submit(self.run_single_experiment, cve_data, keep_results): cve_data
                for cve_data in data
            }
            
            # 收集结果
            for future in as_completed(future_to_cve):
                cve_data = future_to_cve[future]
                try:
                    result = future.result()
                    self.results.append(result)
                    
                    # 打印进度
                    completed = len(self.results)
                    total = self.stats['total']
                    progress = (completed / total) * 100
                    
                    self.logger.info(f"Progress: {completed}/{total} ({progress:.1f}%) - "
                                   f"✅{self.stats['success']} ❌{self.stats['failed']} "
                                   f"⏰{self.stats['timeout']} 🔧{self.stats['setup_failed']}")
                    
                except Exception as e:
                    cve_id = cve_data.get('cve_id', 'unknown')
                    self.logger.error(f"Exception in experiment {cve_id}: {e}")
                    self.stats['failed'] += 1
        
        return self.results
    
    def save_results(self) -> str:
        """保存实验结果"""
        timestamp = int(time.time())
        results_file = self.results_dir / f"experiment_results_{timestamp}.json"
        
        output_data = {
            'timestamp': timestamp,
            'stats': self.stats,
            'results': self.results
        }
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Results saved to {results_file}")
        return str(results_file)
    
    def print_summary(self):
        """打印实验总结"""
        total = self.stats['total']
        if total == 0:
            return
            
        success_rate = (self.stats['success'] / total) * 100
        
        print(f"\n🎉 Experiment Summary:")
        print(f"📊 Total CVEs: {total}")
        print(f"✅ Successful: {self.stats['success']} ({success_rate:.1f}%)")
        print(f"❌ Failed: {self.stats['failed']}")
        print(f"⏰ Timeout: {self.stats['timeout']}")
        print(f"🔧 Setup Failed: {self.stats['setup_failed']}")
        
        # 🚀 新增：仓库设置统计
        total_setups = self.stats['api_setup'] + self.stats['git_setup']
        if total_setups > 0:
            api_rate = (self.stats['api_setup'] / total_setups) * 100
            print(f"\n📋 Repository Setup:")
            print(f"⚡ GitHub API: {self.stats['api_setup']} ({api_rate:.1f}%)")
            print(f"🐌 Git Clone: {self.stats['git_setup']} ({100-api_rate:.1f}%)")
        
        # GitHub API速率信息
        if hasattr(self, 'api_manager'):
            rate_info = self.api_manager.get_rate_limit()
            if rate_info and 'rate' in rate_info:
                remaining = rate_info['rate']['remaining']
                limit = rate_info['rate']['limit']
                used = limit - remaining
                print(f"🔄 GitHub API Used: {used}/{limit}")
        
        # 📁 文件保留信息
        active_experiments = list(self.active_experiments_dir.glob("*")) if self.active_experiments_dir.exists() else []
        if active_experiments:
            print(f"\n📁 实验文件:")
            print(f"🗂️  Active experiments: {len(active_experiments)}")
            print(f"📂 Location: {self.active_experiments_dir}")
            print(f"💡 Diff files in: {{experiment_dir}}/tmp/")
        
        if self.results:
            avg_duration = sum(r.get('duration', 0) for r in self.results) / len(self.results)
            print(f"\n⏱️  Average Duration: {avg_duration:.1f}s")


def main():
    parser = argparse.ArgumentParser(description='Run FixMorph experiments on enhanced dataset')
    parser.add_argument('--data', default='experiments/enhanced_dataset/converted_data.json',
                       help='Converted data file path')
    parser.add_argument('--output', default='experiments/enhanced_dataset',
                       help='Output directory')
    parser.add_argument('--cve-list', nargs='+', help='Specific CVE IDs to run')
    parser.add_argument('--start', type=int, default=0, help='Start index')
    parser.add_argument('--end', type=int, help='End index')
    parser.add_argument('--workers', type=int, default=2, help='Number of parallel workers')
    parser.add_argument('--timeout', type=int, default=3600, help='Timeout per experiment (seconds)')
    parser.add_argument('--keep-results', action='store_true', help='Keep experiment results')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')

    # 断点重连相关参数
    parser.add_argument('--resume', action='store_true', default=True,
                       help='Resume from previous run (default: True)')
    parser.add_argument('--no-resume', action='store_true',
                       help='Start fresh, ignore previous state')
    parser.add_argument('--reset-failed', action='store_true',
                       help='Reset all failed experiments to pending')
    parser.add_argument('--status', action='store_true',
                       help='Show experiment status summary and exit')
    parser.add_argument('--save-checkpoint', type=str,
                       help='Save current state as named checkpoint')
    parser.add_argument('--load-checkpoint', type=str,
                       help='Load state from named checkpoint')
    
    # 阶段2完整验证参数
    parser.add_argument('--stage2', action='store_true',
                       help='Enable Stage2 mode: optimized diff + build verification')
    parser.add_argument('--build-verification', action='store_true',
                       help='Enable build verification (requires stage2)')
    parser.add_argument('--fast-diff-only', action='store_true',
                       help='Stage2 with fast diff only, skip build verification')
    
    # GitHub认证参数
    parser.add_argument('--github-token', type=str,
                       help='GitHub Personal Access Token for authenticated git clone')
    
    args = parser.parse_args()

    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)

    # 处理互斥参数
    resume_mode = args.resume and not args.no_resume

    try:
        # 处理阶段2相关参数
        stage2_mode = args.stage2 or args.build_verification or args.fast_diff_only
        build_verification = args.build_verification and not args.fast_diff_only
        
        if stage2_mode:
            print(f"🚀 启用阶段2模式:")
            print(f"   - 智能diff: ✅ (36,788倍性能提升)")
            print(f"   - 构建验证: {'✅' if build_verification else '⏭️  跳过'}")
        
        # 创建实验运行器
        runner = ExperimentRunner(
            data_file=args.data,
            output_dir=args.output,
            max_workers=args.workers,
            timeout=args.timeout,
            stage2_mode=stage2_mode,
            build_verification=build_verification,
            github_token=args.github_token
        )

        # 处理断点重连相关命令
        if args.status:
            print("📊 Current Experiment Status:")
            runner.checkpoint_manager.print_status_summary()
            return

        if args.load_checkpoint:
            print(f"📥 Loading checkpoint: {args.load_checkpoint}")
            success = runner.checkpoint_manager.load_checkpoint(args.load_checkpoint)
            if success:
                print("✅ Checkpoint loaded successfully")
                runner.checkpoint_manager.print_status_summary()
            else:
                print("❌ Failed to load checkpoint")
                sys.exit(1)
            return

        if args.save_checkpoint:
            print(f"💾 Saving checkpoint: {args.save_checkpoint}")
            checkpoint_file = runner.checkpoint_manager.save_checkpoint(args.save_checkpoint)
            if checkpoint_file:
                print(f"✅ Checkpoint saved to: {checkpoint_file}")
            else:
                print("❌ Failed to save checkpoint")
                sys.exit(1)
            return

        if args.reset_failed:
            print("🔄 Resetting failed experiments...")
            reset_count = runner.checkpoint_manager.reset_failed_experiments()
            print(f"✅ Reset {reset_count} failed experiments")
            runner.checkpoint_manager.print_status_summary()
            return

        # 运行实验
        print(f"🚀 Starting experiments (resume mode: {resume_mode})")
        results = runner.run_batch_experiments(
            cve_list=args.cve_list,
            start_index=args.start,
            end_index=args.end,
            keep_results=args.keep_results,
            resume=resume_mode
        )

        # 保存结果
        results_file = runner.save_results()

        # 打印总结
        runner.print_summary()
        runner.checkpoint_manager.print_status_summary()

        print(f"\n📁 Results saved to: {results_file}")

    except KeyboardInterrupt:
        print("\n🛑 Experiment interrupted by user")
        print("💾 Saving current state...")
        if 'runner' in locals():
            checkpoint_file = runner.checkpoint_manager.save_checkpoint("interrupted")
            print(f"✅ State saved to checkpoint: interrupted")
            print("🔄 Resume with: python experiment_runner.py --resume")
        sys.exit(1)

    except Exception as e:
        print(f"❌ Experiment failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
