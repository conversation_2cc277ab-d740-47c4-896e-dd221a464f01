#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
GitHub Token配置工具

安全地配置GitHub Personal Access Token用于FixMorph实验
"""

import getpass
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))
from src_enhanced import config


def setup_github_token():
    """交互式配置GitHub token"""
    
    print("🔐 GitHub Token配置工具")
    print("=" * 50)
    
    # 检查当前配置
    current_token = config.get_github_token()
    if current_token:
        print(f"✅ 当前已配置GitHub token: {current_token[:8]}...{current_token[-4:]}")
        choice = input("\n是否要更新token？(y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("👋 退出配置")
            return
    
    print("\n📋 请按以下步骤获取GitHub token:")
    print("1. 访问: https://github.com/settings/tokens")
    print("2. 点击 'Generate new token (classic)'")
    print("3. 选择权限: 'repo' (完整仓库访问)")
    print("4. 点击 'Generate token'")
    print("5. 复制生成的token")
    
    print("\n⚠️  重要提醒:")
    print("- token只显示一次，请务必复制保存")
    print("- 不要在聊天或邮件中分享token")
    print("- 这个工具会安全地保存token到本地")
    
    # 安全输入token
    print("\n🔑 请输入您的GitHub token:")
    token = getpass.getpass("Token (输入时不会显示): ").strip()
    
    if not token:
        print("❌ 未输入token，退出配置")
        return
    
    # 验证token格式
    if not token.startswith(('ghp_', 'github_pat_')):
        print("⚠️  警告: token格式可能不正确")
        choice = input("确定要继续吗？(y/N): ").strip().lower()
        if choice not in ['y', 'yes']:
            print("👋 退出配置")
            return
    
    # 保存token
    try:
        config.set_github_token(token)
        print(f"\n🎉 配置成功！")
        print(f"📁 Token已安全保存到: {config.token_file}")
        print(f"🔒 文件权限已设置为用户只读")
        
        # 验证配置
        saved_token = config.get_github_token()
        if saved_token == token:
            print("✅ 配置验证成功")
        else:
            print("❌ 配置验证失败")
            
    except Exception as e:
        print(f"❌ 配置失败: {e}")


def remove_github_token():
    """删除已保存的GitHub token"""
    print("🗑️  删除GitHub Token")
    print("=" * 30)
    
    current_token = config.get_github_token()
    if not current_token:
        print("ℹ️  没有找到已保存的token")
        return
    
    print(f"当前token: {current_token[:8]}...{current_token[-4:]}")
    choice = input("确定要删除吗？(y/N): ").strip().lower()
    
    if choice in ['y', 'yes']:
        config.remove_github_token()
    else:
        print("👋 取消删除")


def show_status():
    """显示当前配置状态"""
    print("📊 GitHub Token状态")
    print("=" * 25)
    
    token = config.get_github_token()
    if token:
        print(f"✅ 已配置: {token[:8]}...{token[-4:]}")
        print(f"📁 位置: {config.token_file}")
    else:
        print("❌ 未配置")
        print("💡 运行 'python src_enhanced/setup_token.py setup' 来配置")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='GitHub Token配置工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 配置token
    subparsers.add_parser('setup', help='配置GitHub token')
    
    # 删除token
    subparsers.add_parser('remove', help='删除GitHub token')
    
    # 显示状态
    subparsers.add_parser('status', help='显示配置状态')
    
    args = parser.parse_args()
    
    if args.command == 'setup':
        setup_github_token()
    elif args.command == 'remove':
        remove_github_token()
    elif args.command == 'status':
        show_status()
    else:
        # 默认运行配置
        setup_github_token()


if __name__ == "__main__":
    main() 