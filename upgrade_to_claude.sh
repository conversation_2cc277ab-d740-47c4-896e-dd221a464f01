#!/bin/bash

# FixMorph 容器升级脚本 - 支持 Claude Code
# 此脚本将帮助你从 Ubuntu 16.04 升级到 Ubuntu 20.04 + Node.js 18 + Claude Code

echo "=== FixMorph 容器升级到 Claude Code 支持版本 ==="
echo ""

# 检查当前环境
echo "📋 当前环境信息："
echo "  - 操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'=' -f2 | tr -d '\"')"
echo "  - Node.js版本: $(node --version 2>/dev/null || echo '未安装')"
echo "  - 当前工作目录: $(pwd)"
echo ""

# 显示数据保护建议
echo "⚠️  重要提醒："
echo "  1. 这个升级需要在物理机/宿主机上操作"
echo "  2. 当前容器的数据将需要备份"
echo "  3. 升级后你将获得 Node.js 18 + Claude Code 支持"
echo ""

# 检查重要文件
echo "📁 检查项目文件："
echo "  - Dockerfile.claude: $([ -f Dockerfile.claude ] && echo '✅ 已创建' || echo '❌ 缺失')"
echo "  - 项目数据目录: $([ -d experiments ] && echo '✅ 存在' || echo '❌ 缺失')"
echo "  - 源代码目录: $([ -d src_enhanced ] && echo '✅ 存在' || echo '❌ 缺失')"
echo ""

echo "🔄 升级步骤指南："
echo ""
echo "在物理机/宿主机上执行以下命令："
echo ""
echo "1️⃣ 备份当前容器数据："
echo "   docker cp <current_container_id>:/home/<USER>/fixmorph ./fixmorph_backup"
echo ""
echo "2️⃣ 构建新的 Claude 支持容器："
echo "   cd ./fixmorph_backup"
echo "   docker build -f Dockerfile.claude -t fixmorph:claude ."
echo ""
echo "3️⃣ 运行新容器："
echo "   docker run -it -v \$(pwd):/home/<USER>/fixmorph fixmorph:claude bash"
echo ""
echo "4️⃣ 在新容器中验证 Claude Code："
echo "   node --version  # 应该显示 v18.x.x"
echo "   claude --help   # 应该显示 Claude Code 帮助信息"
echo ""

# 创建快速脚本
echo "📝 创建快速升级脚本 (upgrade_commands.txt)..."
cat > upgrade_commands.txt << 'EOF'
# 在宿主机上执行这些命令来升级到 Claude Code 支持版本

# 1. 查找当前容器ID
docker ps

# 2. 备份数据 (替换 <container_id> 为实际容器ID)
docker cp <container_id>:/home/<USER>/fixmorph ./fixmorph_backup

# 3. 进入备份目录
cd ./fixmorph_backup

# 4. 构建新容器
docker build -f Dockerfile.claude -t fixmorph:claude .

# 5. 运行新容器
docker run -it -v $(pwd):/home/<USER>/fixmorph fixmorph:claude bash

# 6. 在新容器中测试 Claude Code
node --version
claude --help
cursor --list-extensions
EOF

echo "✅ 已创建 upgrade_commands.txt 文件"
echo ""
echo "🎯 下一步操作："
echo "  1. 退出当前容器: exit"
echo "  2. 在宿主机上查看 upgrade_commands.txt"
echo "  3. 按照指令执行升级"
echo ""
echo "💡 升级完成后，你将拥有："
echo "  ✅ Ubuntu 20.04 (支持最新软件)"
echo "  ✅ Node.js 18 LTS"
echo "  ✅ Claude Code CLI 工具"
echo "  ✅ Claude Code VS Code 扩展支持"
echo "  ✅ 所有原有的 FixMorph 功能"