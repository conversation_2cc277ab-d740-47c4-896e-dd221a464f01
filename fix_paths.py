#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复项目中的硬编码路径

将所有 /FixMorph/ 或 /FIXMOURPF/ 路径替换为当前项目路径
"""

import os
import re
from pathlib import Path

def get_project_root():
    """获取项目根目录"""
    current_path = Path(__file__).resolve()
    
    # 向上查找直到找到项目根目录
    for parent in [current_path] + list(current_path.parents):
        if (parent / "FixMorph.py").exists():
            return parent
        if (parent / "setup.py").exists() and (parent / "app").exists():
            return parent
    
    return Path.cwd()

def fix_file_paths(file_path, project_root):
    """修复单个文件中的路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换 /FixMorph/ 路径
        content = re.sub(r'/FixMorph/', str(project_root) + '/', content)
        
        # 替换 /FIXMOURPF/ 路径（如果存在）
        content = re.sub(r'/FIXMOURPF/', str(project_root) + '/', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ Fixed: {}".format(file_path))
            return True
        else:
            return False

    except Exception as e:
        print("❌ Error fixing {}: {}".format(file_path, e))
        return False

def main():
    """主函数"""
    project_root = get_project_root()
    print("🔧 项目根目录: {}".format(project_root))
    print("🔍 搜索需要修复的文件...")

    # 需要检查的文件类型
    file_patterns = ['*.conf', '*.py', '*.md', '*.txt', '*.json']

    # 需要检查的目录
    search_dirs = ['tests', 'src_enhanced', 'doc', 'experiments']

    fixed_count = 0
    total_checked = 0

    for search_dir in search_dirs:
        search_path = project_root / search_dir
        if not search_path.exists():
            continue

        print("\n📂 检查目录: {}".format(search_dir))

        for pattern in file_patterns:
            for file_path in search_path.rglob(pattern):
                if file_path.is_file():
                    total_checked += 1
                    if fix_file_paths(file_path, project_root):
                        fixed_count += 1

    print("\n🎉 修复完成!")
    print("📊 检查文件: {}".format(total_checked))
    print("✅ 修复文件: {}".format(fixed_count))

    if fixed_count > 0:
        print("\n💡 所有硬编码路径已修复为: {}".format(project_root))
    else:
        print("\n✨ 没有发现需要修复的硬编码路径")

if __name__ == "__main__":
    main()
